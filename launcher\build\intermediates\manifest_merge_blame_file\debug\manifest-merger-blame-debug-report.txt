1<?xml version="1.0" encoding="utf-8"?>
2<!-- GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN -->
3<manifest xmlns:android="http://schemas.android.com/apk/res/android"
4    package="com.cy.exercisetv"
5    android:installLocation="preferExternal"
6    android:versionCode="31"
7    android:versionName="1.3.2" >
8
9    <uses-sdk
10        android:minSdkVersion="21"
10-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml
11        android:targetSdkVersion="30" />
11-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml
12
13    <supports-screens
13-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:4:3-163
14        android:anyDensity="true"
14-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:4:135-160
15        android:largeScreens="true"
15-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:4:78-105
16        android:normalScreens="true"
16-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:4:49-77
17        android:smallScreens="true"
17-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:4:21-48
18        android:xlargeScreens="true" />
18-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:4:106-134
19
20    <uses-feature android:glEsVersion="0x00030000" />
20-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-54
20-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:19-51
21    <uses-feature
21-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-14:36
22        android:name="android.hardware.vulkan.version"
22-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-55
23        android:required="false" />
23-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-33
24    <uses-feature
24-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-17:36
25        android:name="android.hardware.camera"
25-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-47
26        android:required="false" />
26-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:9-33
27    <uses-feature
27-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:5-20:36
28        android:name="android.hardware.camera.autofocus"
28-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-57
29        android:required="false" />
29-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-33
30    <uses-feature
30-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:5-23:36
31        android:name="android.hardware.camera.front"
31-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:9-53
32        android:required="false" />
32-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:9-33
33    <uses-feature
33-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:5-26:36
34        android:name="android.hardware.touchscreen"
34-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-52
35        android:required="false" />
35-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-33
36    <uses-feature
36-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:27:5-29:36
37        android:name="android.hardware.touchscreen.multitouch"
37-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-63
38        android:required="false" />
38-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33
39    <uses-feature
39-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:5-32:36
40        android:name="android.hardware.touchscreen.multitouch.distinct"
40-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:9-72
41        android:required="false" />
41-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:32:9-33
42
43    <uses-permission android:name="android.permission.INTERNET" />
43-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:5-67
43-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:22-64
44    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
44-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:5-81
44-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:22-78
45    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
45-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:5-83
45-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:22-80
46    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
46-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:5-79
46-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:22-76
47    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
47-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:5-76
47-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:22-73
48    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
48-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:5-75
48-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:22-72
49    <uses-permission android:name="android.permission.CAMERA" />
49-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:5-65
49-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:22-62
50    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
50-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:5-76
50-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:22-73
51    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
51-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:5-79
51-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:22-76
52    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
52-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:5-81
52-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:22-78
53    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
53-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:5-79
53-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:22-76
54    <uses-permission android:name="android.permission.WAKE_LOCK" />
54-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:25:5-68
54-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:25:22-65
55    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
55-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:27:5-81
55-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:27:22-78
56    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
56-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:28:5-77
56-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:28:22-74
57    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
57-->[com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:5-79
57-->[com.umeng.umsdk:common:9.8.6] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\84233d18c170e846d5dd10a4684392dc\jetified-common-9.8.6\AndroidManifest.xml:11:22-76
58
59    <application
59-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:5:3-83
60        android:name="com.unity3d.player.App"
60-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:18-55
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.2.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\faae5e4478c326f3f4fa86a104d30d9e\core-1.2.0\AndroidManifest.xml:24:18-86
62        android:debuggable="true"
63        android:icon="@mipmap/app_icon"
63-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:5:49-80
64        android:label="@string/app_name"
64-->E:\huoli-rk3566\hl072202\launcher\src\main\AndroidManifest.xml:5:16-48
65        android:testOnly="true" >
66        <activity
66-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:9-77:20
67            android:name="com.unity3d.player.UnityPlayerActivity"
67-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:13-66
68            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
68-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:13-194
69            android:exported="true"
69-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:13-36
70            android:hardwareAccelerated="false"
70-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:13-48
71            android:launchMode="singleTask"
71-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:13-44
72            android:resizeableActivity="false"
72-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:13-47
73            android:screenOrientation="landscape"
73-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:13-50
74            android:theme="@style/UnityThemeSelector" >
74-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:13-54
75
76            <!-- <intent-filter android:priority="1000"> -->
77            <!-- <action android:name="android.intent.action.MAIN" /> -->
78
79
80            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
81            <!-- <category android:name="android.intent.category.HOME" /> -->
82            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
83            <!-- </intent-filter> -->
84            <intent-filter>
84-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:13-69:29
85                <action android:name="android.intent.action.MAIN" />
85-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:17-69
85-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:25-66
86
87                <category android:name="android.intent.category.LAUNCHER" />
87-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:17-77
87-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:27-74
88            </intent-filter>
89
90            <meta-data
90-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:13-73:40
91                android:name="unityplayer.UnityActivity"
91-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:17-57
92                android:value="true" />
92-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:17-37
93            <meta-data
93-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:13-76:40
94                android:name="android.notch_support"
94-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:17-53
95                android:value="true" />
95-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:17-37
96        </activity>
97
98        <meta-data
98-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:9-81:33
99            android:name="unity.splash-mode"
99-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:13-45
100            android:value="0" />
100-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:13-30
101        <meta-data
101-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:9-84:36
102            android:name="unity.splash-enable"
102-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:13-47
103            android:value="True" />
103-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:84:13-33
104        <meta-data
104-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:85:9-87:37
105            android:name="unity.allow-resizable-window"
105-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:13-56
106            android:value="False" />
106-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:13-34
107        <meta-data
107-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:9-90:50
108            android:name="notch.config"
108-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:13-40
109            android:value="portrait|landscape" />
109-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:13-47
110        <meta-data
110-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:9-93:64
111            android:name="TEST_DEVICE_ID"
111-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:13-42
112            android:value="2A27406B9CA8A5472970D77E263F014F" />
112-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:13-61
113        <meta-data
113-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:9-96:68
114            android:name="unity.build-id"
114-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:13-42
115            android:value="3e5cc2c2-84b1-41e0-ae5f-809a2a5eda1f" />
115-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:13-65
116        <meta-data
116-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:9-99:56
117            android:name="UMENG_APPKEY"
117-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:13-40
118            android:value="686a18c779267e0210a1cb03" />
118-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:13-53
119        <meta-data
119-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:9-102:38
120            android:name="UMENG_CHANNEL"
120-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:13-41
121            android:value="uemeng" />
121-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:13-35
122
123        <provider
123-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:9-112:20
124            android:name="androidx.core.content.FileProvider"
124-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:13-62
125            android:authorities="com.cy.exercisetv.fileprovider"
125-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:13-64
126            android:exported="false"
126-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:13-37
127            android:grantUriPermissions="true" >
127-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:13-47
128            <meta-data
128-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:13-111:54
129                android:name="android.support.FILE_PROVIDER_PATHS"
129-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:17-67
130                android:resource="@xml/file_paths" />
130-->[:unityLibrary] E:\huoli-rk3566\hl072202\unityLibrary\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:17-51
131        </provider>
132        <provider
132-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:31:9-39:20
133            android:name="androidx.startup.InitializationProvider"
133-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:32:13-67
134            android:authorities="com.cy.exercisetv.androidx-startup"
134-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:33:13-68
135            android:exported="false" >
135-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:34:13-37
136            <meta-data
136-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:36:13-38:52
137                android:name="androidx.work.WorkManagerInitializer"
137-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:37:17-68
138                android:value="androidx.startup" />
138-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:38:17-49
139        </provider>
140
141        <service
141-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:41:9-46:35
142            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
142-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:42:13-88
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:43:13-44
144            android:enabled="@bool/enable_system_alarm_service_default"
144-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:44:13-72
145            android:exported="false" />
145-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:45:13-37
146        <service
146-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:47:9-53:35
147            android:name="androidx.work.impl.background.systemjob.SystemJobService"
147-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:48:13-84
148            android:directBootAware="false"
148-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:49:13-44
149            android:enabled="@bool/enable_system_job_service_default"
149-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:50:13-70
150            android:exported="true"
150-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:51:13-36
151            android:permission="android.permission.BIND_JOB_SERVICE" />
151-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:52:13-69
152        <service
152-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:54:9-59:35
153            android:name="androidx.work.impl.foreground.SystemForegroundService"
153-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:55:13-81
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:56:13-44
155            android:enabled="@bool/enable_system_foreground_service_default"
155-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:57:13-77
156            android:exported="false" />
156-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:58:13-37
157
158        <receiver
158-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:61:9-66:35
159            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
159-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:62:13-88
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:63:13-44
161            android:enabled="true"
161-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:64:13-35
162            android:exported="false" />
162-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:65:13-37
163        <receiver
163-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:67:9-77:20
164            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
164-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:68:13-106
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:69:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:70:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:71:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:73:13-76:29
169                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
169-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:74:17-87
169-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:74:25-84
170                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
170-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:75:17-90
170-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:75:25-87
171            </intent-filter>
172        </receiver>
173        <receiver
173-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:78:9-88:20
174            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
174-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:79:13-104
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:80:13-44
176            android:enabled="false"
176-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:81:13-36
177            android:exported="false" >
177-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:82:13-37
178            <intent-filter>
178-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:84:13-87:29
179                <action android:name="android.intent.action.BATTERY_OKAY" />
179-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:85:17-77
179-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:85:25-74
180                <action android:name="android.intent.action.BATTERY_LOW" />
180-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:86:17-76
180-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:86:25-73
181            </intent-filter>
182        </receiver>
183        <receiver
183-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:89:9-99:20
184            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
184-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:90:13-104
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:91:13-44
186            android:enabled="false"
186-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:92:13-36
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:93:13-37
188            <intent-filter>
188-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:95:13-98:29
189                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
189-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:96:17-83
189-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:96:25-80
190                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
190-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:97:17-82
190-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:97:25-79
191            </intent-filter>
192        </receiver>
193        <receiver
193-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:100:9-109:20
194            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
194-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:101:13-103
195            android:directBootAware="false"
195-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:102:13-44
196            android:enabled="false"
196-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:103:13-36
197            android:exported="false" >
197-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:104:13-37
198            <intent-filter>
198-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:106:13-108:29
199                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
199-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:107:17-79
199-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:107:25-76
200            </intent-filter>
201        </receiver>
202        <receiver
202-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:110:9-121:20
203            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
203-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:111:13-88
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:112:13-44
205            android:enabled="false"
205-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:113:13-36
206            android:exported="false" >
206-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:114:13-37
207            <intent-filter>
207-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:116:13-120:29
208                <action android:name="android.intent.action.BOOT_COMPLETED" />
208-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:117:17-79
208-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:117:25-76
209                <action android:name="android.intent.action.TIME_SET" />
209-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:118:17-73
209-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:118:25-70
210                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
210-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:119:17-81
210-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:119:25-78
211            </intent-filter>
212        </receiver>
213        <receiver
213-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:122:9-131:20
214            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
214-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:123:13-99
215            android:directBootAware="false"
215-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:124:13-44
216            android:enabled="@bool/enable_system_alarm_service_default"
216-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:125:13-72
217            android:exported="false" >
217-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:126:13-37
218            <intent-filter>
218-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:128:13-130:29
219                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
219-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:129:17-98
219-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:129:25-95
220            </intent-filter>
221        </receiver>
222        <receiver
222-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:132:9-142:20
223            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
223-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:133:13-78
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:134:13-44
225            android:enabled="true"
225-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:135:13-35
226            android:exported="true"
226-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:136:13-36
227            android:permission="android.permission.DUMP" >
227-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:137:13-57
228            <intent-filter>
228-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:139:13-141:29
229                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
229-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:140:17-88
229-->[androidx.work:work-runtime:2.6.0] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\083e340af32f320e6ad3917bcf6db07c\work-runtime-2.6.0\AndroidManifest.xml:140:25-85
230            </intent-filter>
231        </receiver>
232
233        <service
233-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:10:9-14:41
234            android:name="com.efs.sdk.memleaksdk.monitor.UMonitorService"
234-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:11:13-74
235            android:enabled="true"
235-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:12:13-35
236            android:exported="false"
236-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:13:13-37
237            android:process=":u_heap" />
237-->[com.umeng.umsdk:apm:2.0.4] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\2df79ab5e8464440e8565f182eefca21\jetified-apm-2.0.4\AndroidManifest.xml:14:13-38
238        <service
238-->[androidx.room:room-runtime:2.2.5] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\7cdeb1d41cb3439f6c770ab9c6dbd234\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
239            android:name="androidx.room.MultiInstanceInvalidationService"
239-->[androidx.room:room-runtime:2.2.5] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\7cdeb1d41cb3439f6c770ab9c6dbd234\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
240            android:directBootAware="true"
240-->[androidx.room:room-runtime:2.2.5] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\7cdeb1d41cb3439f6c770ab9c6dbd234\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
241            android:exported="false" />
241-->[androidx.room:room-runtime:2.2.5] E:\gradle\gradle-6.7.1\caches\transforms-2\files-2.1\7cdeb1d41cb3439f6c770ab9c6dbd234\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
242    </application>
243
244</manifest>
