<?xml version="1.0" encoding="utf-8"?><!-- GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.unity3d.player">

  <application android:name=".App">
    <activity
        android:name="com.unity3d.player.UnityPlayerActivity"
        android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
        android:exported="true"
        android:hardwareAccelerated="false"
        android:launchMode="singleTask"
        android:resizeableActivity="false"
        android:screenOrientation="landscape"
        android:theme="@style/UnityThemeSelector">
      <!--      <intent-filter android:priority="1000">-->
      <!--        <action android:name="android.intent.action.MAIN" />-->

      <!--        <category android:name="android.intent.category.LAUNCHER" />-->
      <!--        <category android:name="android.intent.category.HOME" />-->
      <!--        <category android:name="android.intent.category.DEFAULT" />-->
      <!--      </intent-filter>-->
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>

      <meta-data
          android:name="unityplayer.UnityActivity"
          android:value="true" />
      <meta-data
          android:name="android.notch_support"
          android:value="true" />
    </activity>

    <meta-data
        android:name="unity.splash-mode"
        android:value="0" />
    <meta-data
        android:name="unity.splash-enable"
        android:value="True" />
    <meta-data
        android:name="unity.allow-resizable-window"
        android:value="False" />
    <meta-data
        android:name="notch.config"
        android:value="portrait|landscape" />
    <meta-data
        android:name="TEST_DEVICE_ID"
        android:value="2A27406B9CA8A5472970D77E263F014F" />
    <meta-data
        android:name="unity.build-id"
        android:value="3e5cc2c2-84b1-41e0-ae5f-809a2a5eda1f" />

    <meta-data
        android:name="UMENG_APPKEY"
        android:value="686a18c779267e0210a1cb03" />

    <meta-data
        android:name="UMENG_CHANNEL"
        android:value="uemeng"></meta-data>

    <provider
        android:name="androidx.core.content.FileProvider"
        android:authorities="${applicationId}.fileprovider"
        android:exported="false"
        android:grantUriPermissions="true">
      <meta-data
          android:name="android.support.FILE_PROVIDER_PATHS"
          android:resource="@xml/file_paths" />
    </provider>
  </application>

  <uses-feature android:glEsVersion="0x00030000" />
  <uses-feature
      android:name="android.hardware.vulkan.version"
      android:required="false" />

  <uses-feature
      android:name="android.hardware.camera"
      android:required="false" />

  <uses-feature
      android:name="android.hardware.camera.autofocus"
      android:required="false" />
  <uses-feature
      android:name="android.hardware.camera.front"
      android:required="false" />

  <uses-feature
      android:name="android.hardware.touchscreen"
      android:required="false" />

  <uses-feature
      android:name="android.hardware.touchscreen.multitouch"
      android:required="false" />
  <uses-feature
      android:name="android.hardware.touchscreen.multitouch.distinct"
      android:required="false" />

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
</manifest>
