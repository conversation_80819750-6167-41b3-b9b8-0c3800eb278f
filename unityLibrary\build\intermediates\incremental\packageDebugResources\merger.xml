<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res"/><source path="E:\huoli-rk3566\hl072202\unityLibrary\build\generated\res\rs\debug"/><source path="E:\huoli-rk3566\hl072202\unityLibrary\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res"><file name="buttom_img" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\buttom_img.png" qualifiers="" type="drawable"/><file name="button_focus_selector" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\button_focus_selector.xml" qualifiers="" type="drawable"/><file name="button_text_color_selector" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\button_text_color_selector.xml" qualifiers="" type="drawable"/><file name="custom_progress_horizontal" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\custom_progress_horizontal.xml" qualifiers="" type="drawable"/><file name="fqg_btn_img" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\fqg_btn_img.png" qualifiers="" type="drawable"/><file name="gxdialog_bg" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\gxdialog_bg.png" qualifiers="" type="drawable"/><file name="not_not_button_text_color_selector" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\not_not_button_text_color_selector.xml" qualifiers="" type="drawable"/><file name="not_now_button_focus_selector" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\not_now_button_focus_selector.xml" qualifiers="" type="drawable"/><file name="not_now_img" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\not_now_img.png" qualifiers="" type="drawable"/><file name="qg_button_focus_selector" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\qg_button_focus_selector.xml" qualifiers="" type="drawable"/><file name="qzgx_btn_img" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\qzgx_btn_img.png" qualifiers="" type="drawable"/><file name="top_img" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\top_img.png" qualifiers="" type="drawable"/><file name="white_btn_bg" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\drawable\white_btn_bg.xml" qualifiers="" type="drawable"/><file name="update_dialog" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\layout\update_dialog.xml" qualifiers="" type="layout"/><file path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\values\styles.xml" qualifiers=""><style name="UnityThemeSelector" parent="BaseUnityTheme">
	<item name="android:windowBackground">@android:color/black</item>
</style><style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
</style><style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
</style><style name="PrivacyThemeDialog" parent="@android:style/Theme.Dialog">
        
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowIsFloating">true</item>
        
        <item name="android:windowIsTranslucent">true</item>
        
        <item name="android:windowNoTitle">false</item>
        
        <item name="android:background">@android:color/transparent</item>
        
        <item name="android:windowBackground">@android:color/transparent</item>
        
        <item name="android:backgroundDimAmount">0.5</item>
        
        <item name="android:backgroundDimEnabled">true</item>
    </style></file><file path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\values-v21\styles.xml" qualifiers="v21"><style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
</style></file><file path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\values-v28\styles.xml" qualifiers="v28"><style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
    <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
</style></file><file name="file_paths" path="E:\huoli-rk3566\hl072202\unityLibrary\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source><source path="E:\huoli-rk3566\hl072202\unityLibrary\build\generated\res\rs\debug"/><source path="E:\huoli-rk3566\hl072202\unityLibrary\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\huoli-rk3566\hl072202\unityLibrary\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\huoli-rk3566\hl072202\unityLibrary\src\debug\res"/></dataSet><mergedItems/></merger>