1<?xml version="1.0" encoding="utf-8"?>
2<!-- GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN -->
3<manifest xmlns:android="http://schemas.android.com/apk/res/android"
4    xmlns:tools="http://schemas.android.com/tools"
5    package="com.unity3d.player" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml
9        android:targetSdkVersion="30" />
9-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml
10
11    <uses-feature android:glEsVersion="0x00030000" />
11-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:74:3-52
11-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:74:17-49
12    <uses-feature
12-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:75:3-77:34
13        android:name="android.hardware.vulkan.version"
13-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:76:7-53
14        android:required="false" />
14-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:77:7-31
15    <uses-feature
15-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:79:3-81:34
16        android:name="android.hardware.camera"
16-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:80:7-45
17        android:required="false" />
17-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:81:7-31
18    <uses-feature
18-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:83:3-85:34
19        android:name="android.hardware.camera.autofocus"
19-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:84:7-55
20        android:required="false" />
20-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:85:7-31
21    <uses-feature
21-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:86:3-88:34
22        android:name="android.hardware.camera.front"
22-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:87:7-51
23        android:required="false" />
23-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:88:7-31
24    <uses-feature
24-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:90:3-92:34
25        android:name="android.hardware.touchscreen"
25-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:91:7-50
26        android:required="false" />
26-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:92:7-31
27    <uses-feature
27-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:94:3-96:34
28        android:name="android.hardware.touchscreen.multitouch"
28-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:95:7-61
29        android:required="false" />
29-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:96:7-31
30    <uses-feature
30-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:97:3-99:34
31        android:name="android.hardware.touchscreen.multitouch.distinct"
31-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:98:7-70
32        android:required="false" />
32-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:99:7-31
33
34    <uses-permission android:name="android.permission.INTERNET" />
34-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:101:3-65
34-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:101:20-62
35    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
35-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:102:3-79
35-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:102:20-76
36    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
36-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:103:3-81
36-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:103:20-78
37    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
37-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:104:3-77
37-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:104:20-74
38    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
38-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:105:3-74
38-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:105:20-71
39    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
39-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:106:3-73
39-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:106:20-70
40    <uses-permission android:name="android.permission.CAMERA" />
40-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:107:3-63
40-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:107:20-60
41    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
41-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:108:3-74
41-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:108:20-71
42    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
42-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:109:3-77
42-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:109:20-74
43    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
43-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:110:3-79
43-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:110:20-76
44    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
44-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:111:3-77
44-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:111:20-74
45
46    <application android:name="com.unity3d.player.App" >
46-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:6:3-72:17
46-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:6:16-35
47        <activity
47-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:7:5-34:16
48            android:name="com.unity3d.player.UnityPlayerActivity"
48-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:8:9-62
49            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
49-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:9:9-190
50            android:exported="true"
50-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:10:9-32
51            android:hardwareAccelerated="false"
51-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:11:9-44
52            android:launchMode="singleTask"
52-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:12:9-40
53            android:resizeableActivity="false"
53-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:13:9-43
54            android:screenOrientation="landscape"
54-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:14:9-46
55            android:theme="@style/UnityThemeSelector" >
55-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:15:9-50
56
57            <!-- <intent-filter android:priority="1000"> -->
58            <!-- <action android:name="android.intent.action.MAIN" /> -->
59
60
61            <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
62            <!-- <category android:name="android.intent.category.HOME" /> -->
63            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
64            <!-- </intent-filter> -->
65            <intent-filter>
65-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:23:7-26:23
66                <action android:name="android.intent.action.MAIN" />
66-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:24:9-61
66-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:24:17-58
67
68                <category android:name="android.intent.category.LAUNCHER" />
68-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:25:9-69
68-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:25:19-66
69            </intent-filter>
70
71            <meta-data
71-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:28:7-30:34
72                android:name="unityplayer.UnityActivity"
72-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:29:11-51
73                android:value="true" />
73-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:30:11-31
74            <meta-data
74-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:31:7-33:34
75                android:name="android.notch_support"
75-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:32:11-47
76                android:value="true" />
76-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:33:11-31
77        </activity>
78
79        <meta-data
79-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:36:5-38:29
80            android:name="unity.splash-mode"
80-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:37:9-41
81            android:value="0" />
81-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:38:9-26
82        <meta-data
82-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:39:5-41:32
83            android:name="unity.splash-enable"
83-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:40:9-43
84            android:value="True" />
84-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:41:9-29
85        <meta-data
85-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:42:5-44:33
86            android:name="unity.allow-resizable-window"
86-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:43:9-52
87            android:value="False" />
87-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:44:9-30
88        <meta-data
88-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:45:5-47:46
89            android:name="notch.config"
89-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:46:9-36
90            android:value="portrait|landscape" />
90-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:47:9-43
91        <meta-data
91-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:48:5-50:60
92            android:name="TEST_DEVICE_ID"
92-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:49:9-38
93            android:value="2A27406B9CA8A5472970D77E263F014F" />
93-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:50:9-57
94        <meta-data
94-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:51:5-53:64
95            android:name="unity.build-id"
95-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:52:9-38
96            android:value="3e5cc2c2-84b1-41e0-ae5f-809a2a5eda1f" />
96-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:53:9-61
97        <meta-data
97-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:55:5-57:52
98            android:name="UMENG_APPKEY"
98-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:56:9-36
99            android:value="686a18c779267e0210a1cb03" />
99-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:57:9-49
100        <meta-data
100-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:59:5-61:44
101            android:name="UMENG_CHANNEL"
101-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:60:9-37
102            android:value="uemeng" />
102-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:61:9-31
103
104        <provider
104-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:63:5-71:16
105            android:name="androidx.core.content.FileProvider"
105-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:64:9-58
106            android:authorities="${applicationId}.fileprovider"
106-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:65:9-60
107            android:exported="false"
107-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:66:9-33
108            android:grantUriPermissions="true" >
108-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:67:9-43
109            <meta-data
109-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:68:7-70:48
110                android:name="android.support.FILE_PROVIDER_PATHS"
110-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:69:11-61
111                android:resource="@xml/file_paths" />
111-->E:\huoli-rk3566\hl072202\unityLibrary\src\main\AndroidManifest.xml:70:11-45
112        </provider>
113    </application>
114
115</manifest>
